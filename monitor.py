import requests
import csv
import time
from datetime import datetime

def fetch_metrics():
    """......"""
    url = "http://127.0.0.2:1026/metrics-json"
    try:
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"....: {e}")
        return None

def parse_metrics(data):
    """......"""
    metrics = {}
    for item in data.get('result', []):
        metric_name = item['metric'][0]['__name__']
        metrics[metric_name] = item['value']
    return metrics

def write_to_csv(metrics):
    """..CSV.."""
    fieldnames = ['TIME', 'TTFT', 'TBT', 'waitingInferRequestNum', 
                            'processingInferRequestNum', 'remainBlocks']
    with open('metrics_output.csv', 'a', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        
        # ...........
        if f.tell() == 0:
            writer.writeheader()
            
        # ......
        metrics['TIME'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        writer.writerow(metrics)
def main():
    """....."""
    while True:
        raw_data = fetch_metrics()
        if raw_data:
            parsed_data = parse_metrics(raw_data)
            write_to_csv(parsed_data)
        
        # ........
        time.sleep(5 - time.time() % 5)

if __name__ == "__main__":
    main()
