import argparse
import multiprocessing
import time
from multiprocessing import Manager
from multiprocessing.managers import ListProxy
from openai import OpenAI
from openai.types.chat.chat_completion import ChatCompletion
from openai.types.chat.chat_completion_chunk import ChatCompletionChunk
from modelscope import AutoTokenizer
import subprocess
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


def chat_llm(i: int,
             first_token_time_queue: ListProxy, 
             time_queue: ListProxy,
             output_throughput_queue: ListProxy,
             non_first_output_throughput_queue: ListProxy,
             system_throughput_queue: ListProxy,
             prompt_tokens_queue: ListProxy,
             completion_tokens_queue: ListProxy,
             total_tokens_queue: ListProxy):
    """向大语言模型发送聊天请求并收集性能指标
    
    以流式方式处理单个聊天补全请求，测量时间指标，计算token数量，
    并将结果存入共享队列用于性能分析。

    Args:
        i (int): 请求序号，用于标识不同请求
        first_token_time_queue (ListProxy): 共享队列，用于存储首token延迟时间（毫秒）
        time_queue (ListProxy): 共享队列，用于存储请求总耗时（秒）
        output_throughput_queue (ListProxy): 共享队列，用于存储输出token速率（tokens/秒）
        non_first_output_throughput_queue (ListProxy): 共享队列，用于存储输出token速率（tokens/秒）
        system_throughput_queue (ListProxy): 共享队列，用于存储系统token速率（tokens/秒）
        prompt_tokens_queue (ListProxy): 共享队列，用于存储prompt的token数量
        completion_tokens_queue (ListProxy): 共享队列，用于存储补全内容的token数量
        total_tokens_queue (ListProxy): 共享队列，用于存储总token数量（prompt+补全）
    """
    print(f"发送第{i}个请求")

    first_token_time = None # 首个token耗时
    end_time = None # 结束时间
    total_tokens = None # 总token数
    completion_content = "" # 回复内容，需要用这个计算多少个token

    non_first_token_time = None # 非首token耗时
    non_first_token_content  = ""  # 非首token内容

    start_time = time.time() # 请求开始时间

    response: ChatCompletion = client.chat.completions.create(
        model=model_name,
        messages=[{"role": "user", "content": prompt}],
        temperature=0.7,
        stream=True, # 流式返回
        timeout=60*60*24
    )

    for chunk in response:
        chunk: ChatCompletionChunk
        # 取出每次流式响应的内容字段
        delta_content = chunk.choices[0].delta.content or ""

        if first_token_time is None:
            # 首个token耗时
            first_token_time = time.time() - start_time
            first_token_time *= 1000 # 秒 变成 毫秒
            non_first_token_time = time.time()
        else:
            # 非首个token内容
            non_first_token_content += delta_content

        # 收集回复内容，统计token数
        completion_content += delta_content
        if chunk.choices[0].finish_reason == "stop":
            end_time = time.time()
            break
    end_time = time.time()
    
    completion_tokens = len(tokenizer(completion_content)["input_ids"]) # 统计输入的token数
    non_first_tokens = len(tokenizer(non_first_token_content)["input_ids"]) # 统计输出的非首token数
    prompt_tokens = len(tokenizer.apply_chat_template([{"role": "user", "content": prompt}])) # 统计输出的token数
    total_tokens = prompt_tokens + completion_tokens # 总token数

    first_token_time_queue.append(first_token_time) # 首个token时间
    finish_time = end_time - start_time # 总时间
    time_queue.append(finish_time) # 从请求到结束的时间

    prompt_tokens_queue.append(prompt_tokens) # 统计输入的token数
    completion_tokens_queue.append(completion_tokens) # 统计输出的token数
    total_tokens_queue.append(total_tokens) # 总token数

    output_throughput_queue.append(completion_tokens / finish_time) # 输出吞吐量
    non_first_output_throughput_queue.append(non_first_tokens / (end_time - non_first_token_time)) # 非首token输出吞吐量
    system_throughput_queue.append(total_tokens / finish_time) # 系统吞吐量
    
    print(f"第{i}个请求统计完毕")


def test_concurrency(concurrency: int):
    pool = []
    for i in range(concurrency):
        p = multiprocessing.Process(target=chat_llm, 
                                    args=(i,
                                          first_token_time_queue,
                                          time_queue,
                                          output_throughput_queue,
                                          non_first_output_throughput_queue,
                                          system_throughput_queue,
                                          prompt_tokens_queue,
                                          completion_tokens_queue,
                                          total_tokens_queue))
        pool.append(p)
    for p in pool:
        p.start()
    
    for p in pool:
        p.join()

    while True:
        flag = True        
        for i in range(concurrency):
            if pool[i].is_alive():
                flag = False
                break
        if flag:
            break

    assert len(prompt_tokens_queue) == len(completion_tokens_queue) == len(total_tokens_queue) == len(first_token_time_queue) == len(
        time_queue) == concurrency, f"{len(prompt_tokens_queue)}, {len(completion_tokens_queue)}, {len(total_tokens_queue)}, {len(first_token_time_queue)}, {len(time_queue)}, {concurrency}"
    
    with open(metrics_log, "a+") as f:
        f.write(f"====== {concurrency} 并发测试结果=======\n")
        f.write(f"【平均】输入token数 {sum(prompt_tokens_queue) / concurrency} \n")
        f.write(f"【平均】输出token数 {sum(completion_tokens_queue) / concurrency}\n")
        f.write(f"【平均】总token数（输入+输出） {sum(total_tokens_queue) /concurrency}\n")
        f.write(f"【平均】首token时延（ms） {sum(first_token_time_queue) / concurrency}\n")
        f.write(f"【平均】非首token输出token速率（token/s） {sum(non_first_output_throughput_queue) / concurrency}\n")
        f.write(f"【平均】输出token速率（token/s） {sum(output_throughput_queue) / concurrency}\n")
        f.write(f"【平均】系统token速率（token/s） {sum(system_throughput_queue) / concurrency}\n")
        f.write(f"【总】非首token输出吞吐（token/s） {sum(non_first_output_throughput_queue)}\n")
        f.write(f"【总】输出吞吐（token/s） {sum(output_throughput_queue)}\n")
        f.write(f"【总】系统吞吐（token/s） {sum(system_throughput_queue)}\n")
        f.write(f"===================\n")
    
    print(f"====== {concurrency} 并发测试结果=======")
    print("【平均】输入token数", sum(prompt_tokens_queue) / concurrency)
    print("【平均】输出token数", sum(completion_tokens_queue) / concurrency)
    print("【平均】总token数（输入+输出）", sum(total_tokens_queue) /concurrency)
    print("【平均】首token时延（ms）", sum(first_token_time_queue) / concurrency)
    print("【平均】非首token输出token速率（token/s）", sum(non_first_output_throughput_queue) / concurrency)
    print("【平均】输出token速率（token/s）", sum(output_throughput_queue) / concurrency)
    print("【平均】系统token速率（token/s）", sum(system_throughput_queue) / concurrency)
    print("【总】非首token输出吞吐（token/s）", sum(non_first_output_throughput_queue))
    print("【总】输出吞吐（token/s）", sum(output_throughput_queue))
    print("【总】系统吞吐（token/s）", sum(system_throughput_queue))
    print()


def parse_gpu_info(nvidia_smi_output):
    nvidia_smi_output = nvidia_smi_output.split('\n')
    count = len(nvidia_smi_output)
    result_format = "卡{}温度:{} | 卡{}使用率:{} | 卡{}显存占用:{} | 卡{}功率:{} |"
    result = ""
    for i in range(count):
        if nvidia_smi_output[i] == "":
            continue
        info = nvidia_smi_output[i].split(',')
        temp  = f"{info[0]}C"
        util = f"{info[1]}"        
        mem = f"{info[2]}/{info[3]}"
        power = f"{info[4]}"
        result += result_format.format(i, temp, i, util, i, mem, i, power)
    return result


def get_gpu_info():
    return
    # 执行nvidia-smi命令并获取输出
    while True:
        result = subprocess.run(
            ['nvidia-smi', 
             '--query-gpu=temperature.gpu,utilization.gpu,memory.used,memory.total,power.draw',
             '--format=csv,noheader'], 
            stdout=subprocess.PIPE, text=True)
        output = result.stdout
        formatted_output = parse_gpu_info(output)
        print(formatted_output)
        print()
        with open(metrics_log, "a+") as f:
            f.write(formatted_output + "\n")

        time.sleep(10)

def test_pressure():
    gpu_info_process = multiprocessing.Process(target=get_gpu_info)
    gpu_info_process.start()

    while True:
        client.chat.completions.create(
            model=model_name,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.7,
            stream=False,
            timeout=60*60*24
        )


if __name__ == "__main__":

    # 原始输入方式替换为环境变量读取
    model_name = os.getenv('MODEL_NAME', 'Qwen3-32B-AWQ')
    tokenizer_path = os.getenv('TOKENIZER_PATH', '/data/Qwen3-32B-AWQ')
    
    # 并发数处理特殊转换
    concurrency_str = os.getenv('CONCURRENCY', '-1')
    concurrency = list(map(int, concurrency_str.split()))
    
    base_url = os.getenv('BASE_URL', 'http://127.0.0.1:11434/v1')
    api_key = os.getenv('API_KEY', 'sk-xxxxxxx')
    metrics_log = os.getenv('METRICS_LOG', 'qwen3-awq.out')
    prompt_file = os.getenv('PROMPT_FILE', './2k.txt')

    with open(prompt_file, "r") as f:
        prompt = f.read()
    prompt = "将下面内容抄写一遍：" + prompt + " /no_think"

    tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
    client = OpenAI(api_key=api_key, base_url=base_url)

    if concurrency == [-1]:
        test_pressure()
    else:
        for _concurrency in concurrency:
            with Manager() as manager:
                prompt_tokens_queue = manager.list() # 统计输入的token数
                completion_tokens_queue = manager.list() # 统计输出的token数
                total_tokens_queue = manager.list() # 总token数

                first_token_time_queue = manager.list() # 首个token时间
                time_queue = manager.list() # 请求总耗时
                output_throughput_queue = manager.list() # 输出token速率
                non_first_output_throughput_queue = manager.list() # 非首token输出速率
                system_throughput_queue = manager.list() # 系统处理token速率

                test_concurrency(_concurrency)

