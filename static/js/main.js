// 全局变量
let chart = null;
let currentFileInfo = null;

// DOM元素
const fileSelect = document.getElementById('fileSelect');
const startTimeInput = document.getElementById('startTime');
const endTimeInput = document.getElementById('endTime');
const metricsCheckboxes = document.getElementById('metricsCheckboxes');
const updateChartBtn = document.getElementById('updateChart');
const showStatsBtn = document.getElementById('showStats');
const refreshFilesBtn = document.getElementById('refreshFiles');
const fileInfoDiv = document.getElementById('fileInfo');
const fileInfoContent = document.getElementById('fileInfoContent');
const statisticsDiv = document.getElementById('statistics');
const statisticsContent = document.getElementById('statisticsContent');
const loadingDiv = document.getElementById('loading');
const errorDiv = document.getElementById('error');
const errorMessage = document.getElementById('errorMessage');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    loadFileList();
    initChart();
    bindEvents();
});

// 绑定事件
function bindEvents() {
    fileSelect.addEventListener('change', onFileSelect);
    updateChartBtn.addEventListener('click', updateChart);
    showStatsBtn.addEventListener('click', showStatistics);
    refreshFilesBtn.addEventListener('click', loadFileList);
}

// 显示加载状态
function showLoading() {
    loadingDiv.style.display = 'block';
    errorDiv.style.display = 'none';
}

// 隐藏加载状态
function hideLoading() {
    loadingDiv.style.display = 'none';
}

// 显示错误信息
function showError(message) {
    errorMessage.textContent = message;
    errorDiv.style.display = 'block';
    hideLoading();
}

// 隐藏错误信息
function hideError() {
    errorDiv.style.display = 'none';
}

// 加载文件列表
async function loadFileList() {
    showLoading();
    try {
        const response = await fetch('/api/files');
        const result = await response.json();
        
        if (result.success) {
            populateFileSelect(result.files);
            hideError();
        } else {
            showError('加载文件列表失败: ' + result.error);
        }
    } catch (error) {
        showError('网络错误: ' + error.message);
    }
    hideLoading();
}

// 填充文件选择下拉框
function populateFileSelect(files) {
    fileSelect.innerHTML = '<option value="">请选择metrics文件...</option>';
    files.forEach(file => {
        const option = document.createElement('option');
        option.value = file;
        option.textContent = file;
        fileSelect.appendChild(option);
    });
}

// 文件选择事件
async function onFileSelect() {
    const filename = fileSelect.value;
    if (!filename) {
        fileInfoDiv.style.display = 'none';
        metricsCheckboxes.innerHTML = '';
        return;
    }
    
    showLoading();
    try {
        const response = await fetch(`/api/file-info/${filename}`);
        const result = await response.json();
        
        if (result.success) {
            currentFileInfo = result.data;
            displayFileInfo(result.data);
            createMetricsCheckboxes(result.data.columns);
            setDefaultTimeRange(result.data);
            hideError();
        } else {
            showError('加载文件信息失败: ' + result.error);
        }
    } catch (error) {
        showError('网络错误: ' + error.message);
    }
    hideLoading();
}

// 显示文件信息
function displayFileInfo(info) {
    const content = `
        <div class="info-compact">
            <div class="info-item">
                <strong>记录数:</strong> ${info.total_records.toLocaleString()}
            </div>
            <div class="info-item">
                <strong>开始时间:</strong> ${info.start_time}
            </div>
            <div class="info-item">
                <strong>结束时间:</strong> ${info.end_time}
            </div>
        </div>
    `;
    fileInfoContent.innerHTML = content;
    fileInfoDiv.style.display = 'block';
}

// 创建指标复选框
function createMetricsCheckboxes(columns) {
    metricsCheckboxes.innerHTML = '';
    columns.forEach(column => {
        const checkboxItem = document.createElement('div');
        checkboxItem.className = 'checkbox-item';
        
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = `metric_${column}`;
        checkbox.value = column;
        checkbox.checked = true; // 默认全选
        
        const label = document.createElement('label');
        label.htmlFor = `metric_${column}`;
        label.textContent = column;
        
        checkboxItem.appendChild(checkbox);
        checkboxItem.appendChild(label);
        metricsCheckboxes.appendChild(checkboxItem);
    });
}

// 设置默认时间范围
function setDefaultTimeRange(info) {
    // 转换时间格式为datetime-local输入框格式
    const startTime = new Date(info.start_time);
    const defaultEndTime = new Date(info.default_end_time);
    
    startTimeInput.value = formatDateTimeLocal(startTime);
    endTimeInput.value = formatDateTimeLocal(defaultEndTime);
}

// 格式化时间为datetime-local格式
function formatDateTimeLocal(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

// 初始化图表
function initChart() {
    const ctx = document.getElementById('metricsChart').getContext('2d');
    chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: []
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Metrics监控数据',
                    font: {
                        size: 16
                    }
                },
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间'
                    },
                    ticks: {
                        maxTicksLimit: 10
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: '数值'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// 更新图表
async function updateChart() {
    const filename = fileSelect.value;
    if (!filename) {
        alert('请选择一个文件');
        return;
    }

    showLoading();
    try {
        const startTime = startTimeInput.value;
        const endTime = endTimeInput.value;
        const selectedMetrics = getSelectedMetrics();

        if (!startTime || !endTime || selectedMetrics.length === 0) {
            alert('请选择开始时间、结束时间和至少一个指标');
            hideLoading();
            return;
        }

        const params = new URLSearchParams();

        // 添加时间参数
        if (startTime) {
            params.append('start_time', new Date(startTime).toISOString());
        }
        if (endTime) {
            params.append('end_time', new Date(endTime).toISOString());
        }

        // 添加选中的指标
        selectedMetrics.forEach(metric => {
            params.append('columns', metric);
        });

        const response = await fetch(`/api/data/${filename}?${params}`);

        const result = await response.json();

        if (result.success) {
            updateChartData(result.data);
            hideError();
        } else {
            showError('获取数据失败: ' + result.error);
        }
    } catch (error) {
        showError('网络错误: ' + error.message);
    }
    hideLoading();
}

// 获取选中的指标
function getSelectedMetrics() {
    const checkboxes = metricsCheckboxes.querySelectorAll('input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// 更新图表数据
function updateChartData(data) {
    chart.data.labels = data.labels;
    chart.data.datasets = data.datasets;
    chart.update();
}

// 获取随机颜色
function getRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
}

// 显示统计信息
async function showStatistics() {
    const filename = fileSelect.value;
    if (!filename) {
        showError('请先选择一个文件');
        return;
    }

    showLoading();
    try {
        const params = new URLSearchParams({
            start_time: startTimeInput.value ? new Date(startTimeInput.value).toISOString() : '',
            end_time: endTimeInput.value ? new Date(endTimeInput.value).toISOString() : ''
        });

        const response = await fetch(`/api/statistics/${filename}?${params}`);
        const result = await response.json();

        if (result.success) {
            displayStatistics(result.data);
            hideError();
        } else {
            showError('加载统计信息失败: ' + result.error);
        }
    } catch (error) {
        showError('网络错误: ' + error.message);
    }
    hideLoading();
}

// 显示统计信息
function displayStatistics(stats) {
    let content = '<div class="stats-grid">';

    for (const [metric, data] of Object.entries(stats)) {
        content += `
            <div class="stat-card">
                <h4>${metric}</h4>
                <div class="stat-item">
                    <span class="stat-label">平均值:</span>
                    <span class="stat-value">${data.mean.toFixed(2)}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">最小值:</span>
                    <span class="stat-value">${data.min.toFixed(2)}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">最大值:</span>
                    <span class="stat-value">${data.max.toFixed(2)}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">标准差:</span>
                    <span class="stat-value">${data.std.toFixed(2)}</span>
                </div>
            </div>
        `;
    }

    content += '</div>';
    statisticsContent.innerHTML = content;
    statisticsDiv.style.display = 'block';
}
