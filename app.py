from flask import Flask, render_template, jsonify, request
from data_processor import MetricsDataProcessor
import os

app = Flask(__name__)
processor = MetricsDataProcessor()

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/files')
def get_files():
    """获取metrics文件列表"""
    try:
        processor.refresh_file_list()
        files = processor.get_file_list()
        return jsonify({
            'success': True,
            'files': files
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/file-info/<filename>')
def get_file_info(filename):
    """获取文件信息"""
    try:
        info = processor.get_file_info(filename)
        return jsonify({
            'success': True,
            'data': info
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/data/<filename>')
def get_data(filename):
    """获取图表数据"""
    try:
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        columns = request.args.getlist('columns')
        
        # 确保 start_time 和 end_time 不为 None
        if not start_time or not end_time:
            return jsonify({
                'success': False,
                'error': 'Start time and end time are required.'
            }), 400

        data = processor.filter_data(filename, start_time, end_time, columns)
        return jsonify({
            'success': True,
            'data': data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/statistics/<filename>')
def get_statistics(filename):
    """获取统计信息"""
    try:
        start_time = request.args.get('start_time')
        end_time = request.args.get('end_time')
        
        stats = processor.get_statistics(filename, start_time, end_time)
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    # 创建必要的目录
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    os.makedirs('debug', exist_ok=True)
    
    app.run(debug=True, host='0.0.0.0', port=8080)
