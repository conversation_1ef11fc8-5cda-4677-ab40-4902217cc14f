<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大模型服务Metrics分析</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>大模型服务Metrics分析系统</h1>
        </header>
        
        <div class="controls">
            <div class="control-group">
                <label for="fileSelect">选择文件:</label>
                <select id="fileSelect">
                    <option value="">请选择metrics文件...</option>
                </select>
                <button id="refreshFiles">刷新文件列表</button>
            </div>
            
            <div class="control-group">
                <label for="startTime">开始时间:</label>
                <input type="datetime-local" id="startTime">
                
                <label for="endTime">结束时间:</label>
                <input type="datetime-local" id="endTime">
            </div>
            
            <div class="control-group">
                <label>选择指标:</label>
                <div id="metricsCheckboxes" class="checkbox-group">
                    <!-- 动态生成复选框 -->
                </div>
            </div>
            
            <div class="control-group">
                <button id="updateChart">更新图表</button>
                <button id="showStats">显示统计信息</button>
            </div>
        </div>
        
        <div class="file-info" id="fileInfo" style="display: none;">
            <h3>文件信息</h3>
            <div id="fileInfoContent"></div>
        </div>
        
        <div class="chart-container">
            <canvas id="metricsChart"></canvas>
        </div>
        
        <div class="statistics" id="statistics" style="display: none;">
            <h3>统计信息</h3>
            <div id="statisticsContent"></div>
        </div>
        
        <div class="loading" id="loading" style="display: none;">
            <div class="spinner"></div>
            <p>加载中...</p>
        </div>
        
        <div class="error" id="error" style="display: none;">
            <p id="errorMessage"></p>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
