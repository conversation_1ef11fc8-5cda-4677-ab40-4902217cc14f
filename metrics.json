{"resultType": "vector", "result": [{"metric": [{"__name__": "TTFT", "job": "node", "instance": "127.0.0.2:1026"}], "value": "110"}, {"metric": [{"__name__": "TBT", "job": "node", "instance": "127.0.0.2:1026"}], "value": "54"}, {"metric": [{"__name__": "waitingInferRequestNum", "job": "node", "instance": "127.0.0.2:1026"}], "value": "0"}, {"metric": [{"__name__": "processingInferRequestNum", "job": "node", "instance": "127.0.0.2:1026"}], "value": "200"}, {"metric": [{"__name__": "remainBlocks", "job": "node", "instance": "127.0.0.2:1026"}], "value": "446"}]}