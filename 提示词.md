# 监控数据分析
实现一个mindie数据监控系统，需要实现以下功能：
1. 每5秒钟访问 http://127.0.0.2:1026/metrics-json 获取监控数据，格式见下文。
2. 提取监控数据的各字段值，保存到csv中，csv格式见下文。

# json监控数据样例
{
  "resultType": "vector",
  "result": [
    {
      "metric": [
        {
          "__name__": "TTFT",
          "job": "node",
          "instance": "127.0.0.2:1026"
        }
      ],
      "value": "110"
    },
    {
      "metric": [
        {
          "__name__": "TBT",
          "job": "node",
          "instance": "127.0.0.2:1026"
        }
      ],
      "value": "54"
    },
    {
      "metric": [
        {
          "__name__": "waitingInferRequestNum",
          "job": "node",
          "instance": "127.0.0.2:1026"
        }
      ],
      "value": "0"
    },
    {
      "metric": [
        {
          "__name__": "processingInferRequestNum",
          "job": "node",
          "instance": "127.0.0.2:1026"
        }
      ],
      "value": "200"
    },
    {
      "metric": [
        {
          "__name__": "remainBlocks",
          "job": "node",
          "instance": "127.0.0.2:1026"
        }
      ],
      "value": "446"
    }
  ]
}
# csv格式
采集时间,TTFT,TBT,waitingInferRequestNum,processingInferRequestNum,remainBlocks
2023-07-10 10:00:00,110,54,0,200,446
2023-07-10 10:00:05,112,55,0,201,445


# 监控分析程序
## 使用python实现一个大模型服务metrics记录的分析的web程序
metrics记录文件格式为csv，样例如下： 
TIME,TTFT,TBT,waitingInferRequestNum,processingInferRequestNum,remainBlocks
2025-06-09 01:55:09,2288,65,0,21,6035
2025-06-09 01:55:10,2288,65,0,21,6035
2025-06-09 01:55:15,2288,65,0,25,5887
2025-06-09 01:55:20,2288,65,0,27,5813
2025-06-09 01:55:25,2288,65,0,31,5667 .其中：TTFT：首token时延，单位为秒。
TBT：生成连续两个token之间的时间，单位为秒。
processingInferRequestNum：TBT正在执行请求数。
waitingInferRequestNum：正在等待请求数量。
remainBlocks：剩余NPUblock数量。 
##  功能要求：
1. 默认加载当前目录下metrics开头的csv文件。
2. 前端WEB界面采用HTML+CSS+JS实现
3. 前端以折线图显示各指标数据，可以选择指标文件、选择起止时间戳过滤数据、可以选择要展示的监测指标
4. 时间范围默认为所选择文件的第1行记录的时间+1天
